<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Feedback Widget Test</title>
    <meta name="csrf-token" content="test-token">
    
    <!-- html2canvas -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Feedback Widget Styles -->
    <link rel="stylesheet" href="api/app/assets/stylesheets/feedback_widget.css">
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 12px;
            backdrop-filter: blur(10px);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .content {
            line-height: 1.6;
            margin-bottom: 30px;
        }
        
        .feature-box {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .test-instructions {
            background: rgba(255, 255, 255, 0.2);
            padding: 20px;
            border-radius: 8px;
            margin-top: 30px;
        }
        
        .test-instructions h3 {
            margin-top: 0;
        }
        
        .test-instructions ol {
            margin: 0;
            padding-left: 20px;
        }
        
        .test-instructions li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body class="school-layout">
    <div class="container">
        <h1>Feedback Widget Test Page</h1>
        
        <div class="content">
            <p>This is a test page for the feedback widget. The widget should appear as a floating button in the bottom-right corner of the screen.</p>
            
            <div class="feature-box">
                <h3>🎯 Widget Features</h3>
                <ul>
                    <li>Floating feedback button</li>
                    <li>Modal form with message and email fields</li>
                    <li>Screenshot capture functionality</li>
                    <li>Area selection for cropping screenshots</li>
                    <li>Form submission with feedback data</li>
                </ul>
            </div>
            
            <div class="feature-box">
                <h3>📸 Screenshot Testing</h3>
                <p>The screenshot feature should capture this page and allow you to select specific areas. Try selecting different parts of this content to test the area selection functionality.</p>
            </div>
            
            <div class="feature-box">
                <h3>🎨 Visual Elements</h3>
                <p>This colorful background and various UI elements provide good visual content for testing the screenshot capture quality and selection accuracy.</p>
            </div>
        </div>
        
        <div class="test-instructions">
            <h3>Testing Instructions</h3>
            <ol>
                <li>Look for the floating feedback button in the bottom-right corner</li>
                <li>Click the feedback button to open the modal</li>
                <li>Enter some test feedback in the message field</li>
                <li>Optionally enter an email address</li>
                <li>Click "Add Screenshot" to test the screenshot functionality</li>
                <li>Select an area of the page when prompted</li>
                <li>Confirm the selection to see the cropped screenshot preview</li>
                <li>Submit the form (note: this will fail in test mode without a backend)</li>
            </ol>
        </div>
    </div>

    <!-- Feedback Widget JavaScript -->
    <script src="api/app/javascript/area_selector.js"></script>
    <script src="api/app/javascript/screenshot_capture.js"></script>
    <script src="api/app/javascript/feedback_widget.js"></script>
    
    <script>
        // Mock fetch for testing without backend
        const originalFetch = window.fetch;
        window.fetch = function(url, options) {
            if (url === '/feedbacks') {
                console.log('Mock feedback submission:', options.body);
                return Promise.resolve({
                    ok: true,
                    json: () => Promise.resolve({
                        status: 'success',
                        message: 'Test feedback submitted successfully!'
                    })
                });
            }
            return originalFetch.apply(this, arguments);
        };
    </script>
</body>
</html>
