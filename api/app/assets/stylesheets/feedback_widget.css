/* Feedback Widget Styles */

/* Floating Button */
.feedback-floating-btn {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 50px;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: inherit;
}

.feedback-floating-btn:hover {
  background: #0056b3;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 123, 255, 0.4);
}

.feedback-floating-btn:active {
  transform: translateY(0);
}

.feedback-floating-btn svg {
  flex-shrink: 0;
}

/* Modal */
.feedback-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 10000;
  display: none;
  align-items: center;
  justify-content: center;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
}

.feedback-modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.feedback-modal-content {
  position: relative;
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
  animation: feedbackModalSlideIn 0.3s ease-out;
}

@keyframes feedbackModalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.feedback-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #e9ecef;
}

.feedback-modal-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #212529;
}

.feedback-modal-close {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 6px;
  color: #6c757d;
  transition: all 0.2s ease;
}

.feedback-modal-close:hover {
  background: #f8f9fa;
  color: #495057;
}

.feedback-modal-body {
  padding: 24px;
  max-height: calc(90vh - 80px);
  overflow-y: auto;
}

/* Form Styles */
.feedback-form-group {
  margin-bottom: 20px;
}

.feedback-form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #495057;
  font-size: 14px;
}

.feedback-form-group input,
.feedback-form-group textarea {
  width: 100%;
  padding: 12px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  transition: border-color 0.2s ease;
  box-sizing: border-box;
}

.feedback-form-group input:focus,
.feedback-form-group textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.feedback-form-group textarea {
  resize: vertical;
  min-height: 100px;
}

.feedback-form-group small {
  display: block;
  margin-top: 4px;
  color: #6c757d;
  font-size: 12px;
}

/* Screenshot Section */
.feedback-screenshot-section {
  margin-top: 8px;
}

.feedback-screenshot-btn {
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  padding: 16px;
  width: 100%;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 14px;
  color: #495057;
  font-family: inherit;
}

.feedback-screenshot-btn:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.feedback-screenshot-preview {
  position: relative;
  margin-top: 12px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #dee2e6;
}

.feedback-screenshot-preview img {
  width: 100%;
  height: auto;
  max-height: 200px;
  object-fit: contain;
  display: block;
}

.feedback-remove-screenshot {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s ease;
}

.feedback-remove-screenshot:hover {
  background: rgba(0, 0, 0, 0.9);
}

/* Form Actions */
.feedback-form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

.feedback-btn {
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  font-family: inherit;
  display: flex;
  align-items: center;
  gap: 6px;
}

.feedback-btn-secondary {
  background: #f8f9fa;
  color: #495057;
  border: 1px solid #dee2e6;
}

.feedback-btn-secondary:hover {
  background: #e9ecef;
}

.feedback-btn-primary {
  background: #007bff;
  color: white;
}

.feedback-btn-primary:hover:not(:disabled) {
  background: #0056b3;
}

.feedback-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.feedback-submit-loading {
  display: none;
  align-items: center;
  gap: 6px;
}

/* Success State */
.feedback-success {
  text-align: center;
  padding: 20px;
}

.feedback-success-icon {
  margin-bottom: 16px;
}

.feedback-success-icon svg {
  color: #28a745;
  margin: 0 auto;
}

.feedback-success h3 {
  margin: 0 0 12px 0;
  color: #212529;
  font-size: 18px;
}

.feedback-success p {
  margin: 0 0 20px 0;
  color: #6c757d;
  line-height: 1.5;
}

/* Loading Indicator */
.feedback-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 10001;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feedback-loading-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
}

.feedback-loading-content {
  position: relative;
  background: white;
  border-radius: 12px;
  padding: 32px;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.feedback-loading-spinner {
  margin-bottom: 16px;
}

.feedback-loading-content p {
  margin: 0;
  color: #495057;
  font-size: 14px;
}

/* Spinner Animation */
.feedback-spinner {
  animation: feedbackSpin 1s linear infinite;
}

@keyframes feedbackSpin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Screenshot Overlay */
.feedback-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 10002;
  cursor: crosshair;
  user-select: none;
}

.feedback-overlay-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
}

.feedback-overlay-canvas {
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0.9;
}

.feedback-selection-rectangle {
  position: absolute;
  border: 2px solid #007bff;
  background: rgba(0, 123, 255, 0.1);
  display: none;
  pointer-events: none;
}

.feedback-selection-rectangle::before {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  border: 1px solid rgba(255, 255, 255, 0.8);
  pointer-events: none;
}

/* Overlay Instructions */
.feedback-overlay-instructions {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10003;
}

.feedback-instructions-content {
  background: white;
  border-radius: 12px;
  padding: 20px 24px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  text-align: center;
  max-width: 400px;
}

.feedback-instructions-content h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #212529;
}

.feedback-instructions-content p {
  margin: 0 0 20px 0;
  color: #6c757d;
  font-size: 14px;
  line-height: 1.4;
}

.feedback-instructions-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .feedback-floating-btn {
    bottom: 16px;
    right: 16px;
    padding: 10px 16px;
    font-size: 13px;
  }

  .feedback-modal-content {
    width: 95%;
    margin: 16px;
  }

  .feedback-modal-header,
  .feedback-modal-body {
    padding: 16px 20px;
  }

  .feedback-form-actions {
    flex-direction: column-reverse;
  }

  .feedback-btn {
    width: 100%;
    justify-content: center;
  }

  .feedback-instructions-content {
    margin: 0 16px;
    padding: 16px 20px;
  }

  .feedback-instructions-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .feedback-floating-btn span {
    display: none;
  }

  .feedback-floating-btn {
    border-radius: 50%;
    padding: 12px;
  }
}
