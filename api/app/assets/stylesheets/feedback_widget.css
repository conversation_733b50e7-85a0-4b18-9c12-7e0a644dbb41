/* Feedback Widget Styles - Minimal CSS for Tailwind Integration */

/* Custom animations that can't be done with Tailwind */
@keyframes feedbackModalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes feedbackSpin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Animation classes */
.feedback-modal-content {
  animation: feedbackModalSlideIn 0.3s ease-out;
}

.feedback-spinner {
  animation: feedbackSpin 1s linear infinite;
}

/* Custom box shadows */
.feedback-floating-btn {
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.feedback-floating-btn:hover {
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
}

/* Backdrop effects */
.feedback-modal-backdrop {
  backdrop-filter: blur(4px);
}

/* Focus styles */
.feedback-form-group input:focus,
.feedback-form-group textarea:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Selection rectangle border effect */
.feedback-selection-rectangle::before {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  border: 1px solid rgba(255, 255, 255, 0.8);
  pointer-events: none;
}


