// Screenshot Capture System - Handles capturing page screenshots using html2canvas
class ScreenshotCapture {
  constructor() {
    this.areaSelector = null;
  }

  async captureFullPage() {
    try {
      // Show loading indicator
      this.showLoadingIndicator();

      // Temporarily hide the feedback modal and floating button
      const elementsToHide = this.hideWidgetElements();

      // Configure html2canvas options for better quality and compatibility
      const options = {
        allowTaint: true,
        useCORS: true,
        scale: 1,
        scrollX: 0,
        scrollY: 0,
        width: window.innerWidth,
        height: window.innerHeight,
        backgroundColor: '#ffffff',
        removeContainer: true
      };

      const canvas = await html2canvas(document.body, options);

      // Restore hidden elements
      this.restoreWidgetElements(elementsToHide);

      this.hideLoadingIndicator();
      return canvas;
    } catch (error) {
      this.hideLoadingIndicator();
      console.error('Failed to capture page:', error);
      throw new Error('Failed to capture page screenshot');
    }
  }

  hideWidgetElements() {
    const elementsToHide = [];

    // Hide feedback modal completely by setting display none
    const modal = document.getElementById('feedback-modal');
    if (modal && !modal.classList.contains('hidden')) {
      // Store original display state
      const originalDisplay = modal.style.display;
      modal.style.display = 'none';
      elementsToHide.push({
        element: modal,
        wasVisible: true,
        originalDisplay: originalDisplay,
        wasModalOpen: true
      });
    }

    // Hide floating button
    const floatingBtn = document.getElementById('feedback-floating-btn');
    if (floatingBtn) {
      const originalDisplay = floatingBtn.style.display;
      floatingBtn.style.display = 'none';
      elementsToHide.push({
        element: floatingBtn,
        wasVisible: true,
        originalDisplay: originalDisplay
      });
    }

    return elementsToHide;
  }

  restoreWidgetElements(elementsToHide) {
    elementsToHide.forEach(({ element, wasVisible, isBackdrop }) => {
      if (wasVisible) {
        if (element.id === 'feedback-modal') {
          element.classList.remove('hidden');
          element.classList.add('flex');
        } else if (element.id === 'feedback-floating-btn') {
          element.style.display = '';
        } else if (isBackdrop) {
          element.style.display = '';
        }
      }
    });
  }

  async startSelection() {
    try {
      // First capture the full page
      const fullCanvas = await this.captureFullPage();
      
      // Create area selector with the captured canvas
      this.areaSelector = new AreaSelector(fullCanvas);
      
      // Wait for user to make a selection
      const selectedArea = await this.areaSelector.waitForSelection();
      
      if (selectedArea) {
        // Crop the selected area and return as data URL
        const croppedCanvas = this.cropSelection(fullCanvas, selectedArea);
        return croppedCanvas.toDataURL('image/png', 0.8);
      }
      
      return null;
    } catch (error) {
      console.error('Screenshot selection failed:', error);
      throw error;
    } finally {
      // Clean up
      if (this.areaSelector) {
        this.areaSelector.cleanup();
        this.areaSelector = null;
      }
    }
  }

  cropSelection(sourceCanvas, selection) {
    const croppedCanvas = document.createElement('canvas');
    const ctx = croppedCanvas.getContext('2d');

    // Set canvas size to selection size
    croppedCanvas.width = selection.width;
    croppedCanvas.height = selection.height;

    // Draw the selected area from source canvas to cropped canvas
    ctx.drawImage(
      sourceCanvas,
      selection.x, selection.y,           // Source position
      selection.width, selection.height, // Source size
      0, 0,                              // Destination position
      selection.width, selection.height  // Destination size
    );

    return croppedCanvas;
  }

  showLoadingIndicator() {
    // Remove any existing loading indicator
    this.hideLoadingIndicator();

    const loading = document.createElement('div');
    loading.id = 'feedback-screenshot-loading';
    loading.className = 'fixed inset-0 z-[10001] flex items-center justify-center';
    loading.innerHTML = `
      <div class="absolute inset-0 bg-black bg-opacity-70"></div>
      <div class="relative bg-white rounded-xl p-8 text-center shadow-2xl">
        <div class="mb-4">
          <svg class="feedback-spinner mx-auto" width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2v4M12 18v4M4.93 4.93l2.83 2.83M16.24 16.24l2.83 2.83M2 12h4M18 12h4M4.93 19.07l2.83-2.83M16.24 7.76l2.83-2.83" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <p class="text-gray-700 text-sm m-0">Capturing screenshot...</p>
      </div>
    `;

    document.body.appendChild(loading);
  }

  hideLoadingIndicator() {
    const loading = document.getElementById('feedback-screenshot-loading');
    if (loading) {
      loading.remove();
    }
  }
}

// Make it available globally
window.ScreenshotCapture = ScreenshotCapture;
