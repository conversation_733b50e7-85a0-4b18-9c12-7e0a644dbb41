// Area Selector - Handles the overlay and selection rectangle for screenshot cropping
class AreaSelector {
  constructor(sourceCanvas) {
    this.sourceCanvas = sourceCanvas;
    this.overlay = null;
    this.selection = { x: 0, y: 0, width: 0, height: 0 };
    this.isSelecting = false;
    this.startPoint = { x: 0, y: 0 };
    this.selectionPromise = null;
    this.resolveSelection = null;
    this.rejectSelection = null;
  }

  waitForSelection() {
    return new Promise((resolve, reject) => {
      this.resolveSelection = resolve;
      this.rejectSelection = reject;
      this.createOverlay();
    });
  }

  createOverlay() {
    // Create overlay container
    this.overlay = document.createElement('div');
    this.overlay.className = 'fixed inset-0 z-[10002] cursor-crosshair select-none';
    this.overlay.innerHTML = `
      <div class="absolute inset-0 bg-black bg-opacity-30"></div>
      <canvas class="absolute inset-0 opacity-90 feedback-overlay-canvas"></canvas>
      <div class="feedback-selection-rectangle absolute border-2 border-blue-600 bg-blue-600 bg-opacity-10 hidden pointer-events-none"></div>
      <div class="absolute top-5 left-1/2 transform -translate-x-1/2 z-[10003]">
        <div class="bg-white rounded-xl p-6 shadow-2xl text-center max-w-md mx-4">
          <h3 class="text-base font-semibold text-gray-900 mb-2 mt-0">Select an area to capture</h3>
          <p class="text-sm text-gray-600 leading-relaxed mb-5 mt-0">Click and drag to select the area you want to include in your feedback</p>
          <div class="flex gap-3 justify-center">
            <button type="button" id="feedback-selection-cancel" class="px-5 py-2.5 rounded-md text-sm font-medium cursor-pointer transition-all duration-200 border-0 flex items-center gap-1.5 bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200">
              Cancel
            </button>
            <button type="button" id="feedback-selection-confirm" class="px-5 py-2.5 rounded-md text-sm font-medium cursor-pointer transition-all duration-200 border-0 flex items-center gap-1.5 bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-60 disabled:cursor-not-allowed" disabled>
              Capture Selected Area
            </button>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(this.overlay);

    // Set up the canvas with the screenshot
    this.setupCanvas();

    // Bind events
    this.bindEvents();

    // Prevent scrolling while overlay is active
    document.body.style.overflow = 'hidden';
  }

  setupCanvas() {
    const canvas = this.overlay.querySelector('canvas');
    const ctx = canvas.getContext('2d');

    // Set canvas size to viewport size
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;

    // Calculate scaling to fit the source canvas in the viewport
    const scaleX = canvas.width / this.sourceCanvas.width;
    const scaleY = canvas.height / this.sourceCanvas.height;
    const scale = Math.min(scaleX, scaleY);

    // Calculate centered position
    const scaledWidth = this.sourceCanvas.width * scale;
    const scaledHeight = this.sourceCanvas.height * scale;
    const offsetX = (canvas.width - scaledWidth) / 2;
    const offsetY = (canvas.height - scaledHeight) / 2;

    // Store scaling info for coordinate conversion
    this.canvasScale = {
      scale: scale,
      offsetX: offsetX,
      offsetY: offsetY,
      scaledWidth: scaledWidth,
      scaledHeight: scaledHeight
    };

    // Draw the screenshot on the canvas
    ctx.drawImage(this.sourceCanvas, offsetX, offsetY, scaledWidth, scaledHeight);
  }

  bindEvents() {
    const canvas = this.overlay.querySelector('canvas');
    const selectionRect = this.overlay.querySelector('.feedback-selection-rectangle');
    const cancelBtn = document.getElementById('feedback-selection-cancel');
    const confirmBtn = document.getElementById('feedback-selection-confirm');

    // Mouse events for selection
    canvas.addEventListener('mousedown', (e) => this.handleMouseDown(e));
    canvas.addEventListener('mousemove', (e) => this.handleMouseMove(e));
    canvas.addEventListener('mouseup', (e) => this.handleMouseUp(e));

    // Touch events for mobile support
    canvas.addEventListener('touchstart', (e) => this.handleTouchStart(e));
    canvas.addEventListener('touchmove', (e) => this.handleTouchMove(e));
    canvas.addEventListener('touchend', (e) => this.handleTouchEnd(e));

    // Button events
    cancelBtn.addEventListener('click', () => this.cancel());
    confirmBtn.addEventListener('click', () => this.confirm());

    // Escape key to cancel
    document.addEventListener('keydown', this.handleKeyDown.bind(this));

    // Prevent context menu
    canvas.addEventListener('contextmenu', (e) => e.preventDefault());
  }

  handleMouseDown(e) {
    e.preventDefault();
    const rect = e.target.getBoundingClientRect();
    this.startPoint = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    };
    this.isSelecting = true;
    this.updateSelection(this.startPoint.x, this.startPoint.y, 0, 0);
  }

  handleMouseMove(e) {
    if (!this.isSelecting) return;
    
    e.preventDefault();
    const rect = e.target.getBoundingClientRect();
    const currentX = e.clientX - rect.left;
    const currentY = e.clientY - rect.top;

    const x = Math.min(this.startPoint.x, currentX);
    const y = Math.min(this.startPoint.y, currentY);
    const width = Math.abs(currentX - this.startPoint.x);
    const height = Math.abs(currentY - this.startPoint.y);

    this.updateSelection(x, y, width, height);
  }

  handleMouseUp(e) {
    if (!this.isSelecting) return;
    
    e.preventDefault();
    this.isSelecting = false;
    this.updateConfirmButton();
  }

  // Touch event handlers for mobile support
  handleTouchStart(e) {
    e.preventDefault();
    if (e.touches.length === 1) {
      const touch = e.touches[0];
      const rect = e.target.getBoundingClientRect();
      this.startPoint = {
        x: touch.clientX - rect.left,
        y: touch.clientY - rect.top
      };
      this.isSelecting = true;
      this.updateSelection(this.startPoint.x, this.startPoint.y, 0, 0);
    }
  }

  handleTouchMove(e) {
    if (!this.isSelecting || e.touches.length !== 1) return;
    
    e.preventDefault();
    const touch = e.touches[0];
    const rect = e.target.getBoundingClientRect();
    const currentX = touch.clientX - rect.left;
    const currentY = touch.clientY - rect.top;

    const x = Math.min(this.startPoint.x, currentX);
    const y = Math.min(this.startPoint.y, currentY);
    const width = Math.abs(currentX - this.startPoint.x);
    const height = Math.abs(currentY - this.startPoint.y);

    this.updateSelection(x, y, width, height);
  }

  handleTouchEnd(e) {
    if (!this.isSelecting) return;
    
    e.preventDefault();
    this.isSelecting = false;
    this.updateConfirmButton();
  }

  handleKeyDown(e) {
    if (e.key === 'Escape') {
      this.cancel();
    }
  }

  updateSelection(x, y, width, height) {
    // Constrain selection to canvas bounds
    const canvas = this.overlay.querySelector('.feedback-overlay-canvas');
    const maxX = canvas.width;
    const maxY = canvas.height;

    x = Math.max(0, Math.min(x, maxX));
    y = Math.max(0, Math.min(y, maxY));
    width = Math.min(width, maxX - x);
    height = Math.min(height, maxY - y);

    // Store selection in canvas coordinates
    this.selection = { x, y, width, height };

    // Update visual selection rectangle
    const selectionRect = this.overlay.querySelector('.feedback-selection-rectangle');
    selectionRect.style.left = `${x}px`;
    selectionRect.style.top = `${y}px`;
    selectionRect.style.width = `${width}px`;
    selectionRect.style.height = `${height}px`;

    if (width > 5 && height > 5) {
      selectionRect.classList.remove('hidden');
    } else {
      selectionRect.classList.add('hidden');
    }

    this.updateConfirmButton();
  }

  updateConfirmButton() {
    const confirmBtn = document.getElementById('feedback-selection-confirm');
    const hasValidSelection = this.selection.width > 10 && this.selection.height > 10;
    confirmBtn.disabled = !hasValidSelection;
  }

  convertToSourceCoordinates() {
    // Convert canvas coordinates back to source image coordinates
    const { scale, offsetX, offsetY } = this.canvasScale;
    
    return {
      x: Math.round((this.selection.x - offsetX) / scale),
      y: Math.round((this.selection.y - offsetY) / scale),
      width: Math.round(this.selection.width / scale),
      height: Math.round(this.selection.height / scale)
    };
  }

  confirm() {
    if (this.selection.width > 10 && this.selection.height > 10) {
      const sourceSelection = this.convertToSourceCoordinates();
      this.resolveSelection(sourceSelection);
    } else {
      this.cancel();
    }
  }

  cancel() {
    this.resolveSelection(null);
  }

  cleanup() {
    if (this.overlay) {
      document.body.removeChild(this.overlay);
      this.overlay = null;
    }
    
    // Restore scrolling
    document.body.style.overflow = '';
    
    // Remove event listeners
    document.removeEventListener('keydown', this.handleKeyDown.bind(this));
  }
}

// Make it available globally
window.AreaSelector = AreaSelector;
