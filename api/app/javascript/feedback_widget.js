// Feedback Widget - Main class that manages the floating button and modal
class FeedbackWidget {
  constructor() {
    this.modal = null;
    this.screenshotBlob = null;
    this.screenshotCapture = null;
    this.isSubmitting = false;
    this.init();
  }

  init() {
    // Only initialize if we're in the school area
    if (!document.body.classList.contains('school-layout') && 
        !window.location.pathname.startsWith('/school')) {
      return;
    }

    this.createFloatingButton();
    this.createModal();
    this.bindEvents();
    
    // Initialize screenshot capture system
    this.screenshotCapture = new ScreenshotCapture();
  }

  createFloatingButton() {
    const button = document.createElement('button');
    button.id = 'feedback-floating-btn';
    button.className = 'feedback-floating-btn';
    button.innerHTML = `
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M8 9h8M8 13h6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
      <span>Feedback</span>
    `;
    button.setAttribute('aria-label', 'Open feedback form');
    button.title = 'Send us feedback about this page';
    
    document.body.appendChild(button);
  }

  createModal() {
    const modal = document.createElement('div');
    modal.id = 'feedback-modal';
    modal.className = 'feedback-modal';
    modal.innerHTML = `
      <div class="feedback-modal-backdrop"></div>
      <div class="feedback-modal-content">
        <div class="feedback-modal-header">
          <h2>Send Feedback</h2>
          <button type="button" class="feedback-modal-close" aria-label="Close feedback form">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>
        </div>
        
        <div class="feedback-modal-body">
          <form id="feedback-form">
            <div class="feedback-form-group">
              <label for="feedback-message">Your feedback *</label>
              <textarea 
                id="feedback-message" 
                name="message" 
                required 
                placeholder="Tell us what you think about this page, what's working well, or what could be improved..."
                rows="4"
              ></textarea>
            </div>
            
            <div class="feedback-form-group">
              <label for="feedback-email">Email (optional)</label>
              <input 
                type="email" 
                id="feedback-email" 
                name="email" 
                placeholder="<EMAIL>"
              />
              <small>Leave your email if you'd like us to follow up with you</small>
            </div>
            
            <div class="feedback-form-group">
              <div class="feedback-screenshot-section">
                <button type="button" id="add-screenshot-btn" class="feedback-screenshot-btn">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <circle cx="12" cy="13" r="4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  Add Screenshot
                </button>
                <div id="screenshot-preview" class="feedback-screenshot-preview" style="display: none;">
                  <img id="screenshot-image" alt="Screenshot preview" />
                  <button type="button" id="remove-screenshot-btn" class="feedback-remove-screenshot">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
            
            <div class="feedback-form-actions">
              <button type="button" id="feedback-cancel-btn" class="feedback-btn feedback-btn-secondary">
                Cancel
              </button>
              <button type="submit" id="feedback-submit-btn" class="feedback-btn feedback-btn-primary">
                <span class="feedback-submit-text">Send Feedback</span>
                <span class="feedback-submit-loading" style="display: none;">
                  <svg class="feedback-spinner" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2v4M12 18v4M4.93 4.93l2.83 2.83M16.24 16.24l2.83 2.83M2 12h4M18 12h4M4.93 19.07l2.83-2.83M16.24 7.76l2.83-2.83" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  Sending...
                </span>
              </button>
            </div>
          </form>
          
          <div id="feedback-success" class="feedback-success" style="display: none;">
            <div class="feedback-success-icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M22 4L12 14.01l-3-3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <h3>Thank you for your feedback!</h3>
            <p>We appreciate you taking the time to help us improve. Your feedback has been sent to our team.</p>
            <button type="button" id="feedback-success-close" class="feedback-btn feedback-btn-primary">
              Close
            </button>
          </div>
        </div>
      </div>
    `;
    
    document.body.appendChild(modal);
    this.modal = modal;
  }

  bindEvents() {
    const floatingBtn = document.getElementById('feedback-floating-btn');
    const modal = document.getElementById('feedback-modal');
    const closeBtn = modal.querySelector('.feedback-modal-close');
    const cancelBtn = document.getElementById('feedback-cancel-btn');
    const backdrop = modal.querySelector('.feedback-modal-backdrop');
    const form = document.getElementById('feedback-form');
    const addScreenshotBtn = document.getElementById('add-screenshot-btn');
    const removeScreenshotBtn = document.getElementById('remove-screenshot-btn');
    const successCloseBtn = document.getElementById('feedback-success-close');

    // Open modal
    floatingBtn.addEventListener('click', () => this.openModal());

    // Close modal
    closeBtn.addEventListener('click', () => this.closeModal());
    cancelBtn.addEventListener('click', () => this.closeModal());
    backdrop.addEventListener('click', () => this.closeModal());
    successCloseBtn.addEventListener('click', () => this.closeModal());

    // Form submission
    form.addEventListener('submit', (e) => this.handleSubmit(e));

    // Screenshot functionality
    addScreenshotBtn.addEventListener('click', () => this.addScreenshot());
    removeScreenshotBtn.addEventListener('click', () => this.removeScreenshot());

    // Escape key to close modal
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isModalOpen()) {
        this.closeModal();
      }
    });
  }

  openModal() {
    this.modal.style.display = 'flex';
    document.body.style.overflow = 'hidden';
    
    // Focus the message textarea
    setTimeout(() => {
      document.getElementById('feedback-message').focus();
    }, 100);
  }

  closeModal() {
    this.modal.style.display = 'none';
    document.body.style.overflow = '';
    this.resetForm();
  }

  isModalOpen() {
    return this.modal && this.modal.style.display === 'flex';
  }

  resetForm() {
    const form = document.getElementById('feedback-form');
    const successDiv = document.getElementById('feedback-success');
    
    form.reset();
    form.style.display = 'block';
    successDiv.style.display = 'none';
    this.removeScreenshot();
    this.isSubmitting = false;
    this.updateSubmitButton();
  }

  async addScreenshot() {
    try {
      const screenshotData = await this.screenshotCapture.startSelection();
      if (screenshotData) {
        this.screenshotBlob = screenshotData;
        this.showScreenshotPreview(screenshotData);
      }
    } catch (error) {
      console.error('Failed to capture screenshot:', error);
      this.showError('Failed to capture screenshot. Please try again.');
    }
  }

  removeScreenshot() {
    this.screenshotBlob = null;
    const preview = document.getElementById('screenshot-preview');
    preview.style.display = 'none';
  }

  showScreenshotPreview(screenshotData) {
    const preview = document.getElementById('screenshot-preview');
    const img = document.getElementById('screenshot-image');
    
    img.src = screenshotData;
    preview.style.display = 'block';
  }

  async handleSubmit(e) {
    e.preventDefault();
    
    if (this.isSubmitting) return;
    
    this.isSubmitting = true;
    this.updateSubmitButton();

    const formData = new FormData(e.target);
    const data = {
      message: formData.get('message'),
      email: formData.get('email'),
      page_url: window.location.href
    };

    // Add screenshot if present
    if (this.screenshotBlob) {
      data.screenshot = this.screenshotBlob.split(',')[1]; // Remove data URL prefix
    }

    try {
      const response = await fetch('/feedbacks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify(data)
      });

      const result = await response.json();

      if (response.ok && result.status === 'success') {
        this.showSuccess();
      } else {
        throw new Error(result.errors ? result.errors.join(', ') : 'Failed to submit feedback');
      }
    } catch (error) {
      console.error('Failed to submit feedback:', error);
      this.showError(error.message || 'Failed to submit feedback. Please try again.');
    } finally {
      this.isSubmitting = false;
      this.updateSubmitButton();
    }
  }

  updateSubmitButton() {
    const submitBtn = document.getElementById('feedback-submit-btn');
    const submitText = submitBtn.querySelector('.feedback-submit-text');
    const submitLoading = submitBtn.querySelector('.feedback-submit-loading');
    
    if (this.isSubmitting) {
      submitBtn.disabled = true;
      submitText.style.display = 'none';
      submitLoading.style.display = 'flex';
    } else {
      submitBtn.disabled = false;
      submitText.style.display = 'block';
      submitLoading.style.display = 'none';
    }
  }

  showSuccess() {
    const form = document.getElementById('feedback-form');
    const successDiv = document.getElementById('feedback-success');
    
    form.style.display = 'none';
    successDiv.style.display = 'block';
  }

  showError(message) {
    // Simple error display - could be enhanced with a proper notification system
    alert(`Error: ${message}`);
  }
}

// Auto-initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.feedbackWidget = new FeedbackWidget();
});
