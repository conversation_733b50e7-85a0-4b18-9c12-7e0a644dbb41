// Feedback Widget - Main class that manages the floating button and modal
class FeedbackWidget {
  constructor() {
    this.modal = null;
    this.screenshotBlob = null;
    this.screenshotCapture = null;
    this.isSubmitting = false;
    this.init();
  }

  init() {
    // Only initialize if we're in the school area
    if (!document.body.classList.contains('school-layout') && 
        !window.location.pathname.startsWith('/school')) {
      return;
    }

    this.createFloatingButton();
    this.createModal();
    this.bindEvents();
    
    // Initialize screenshot capture system
    this.screenshotCapture = new ScreenshotCapture();
  }

  createFloatingButton() {
    const button = document.createElement('button');
    button.id = 'feedback-floating-btn';
    button.className = 'feedback-floating-btn fixed bottom-5 right-5 z-[1000] bg-blue-600 hover:bg-blue-700 text-white border-0 rounded-full px-5 py-3 text-sm font-semibold cursor-pointer shadow-lg transition-all duration-300 flex items-center gap-2 hover:-translate-y-0.5 active:translate-y-0';
    button.innerHTML = `
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="flex-shrink-0">
        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M8 9h8M8 13h6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
      <span class="md:inline hidden">Feedback</span>
    `;
    button.setAttribute('aria-label', 'Open feedback form');
    button.title = 'Send us feedback about this page';

    document.body.appendChild(button);
  }

  createModal() {
    const modal = document.createElement('div');
    modal.id = 'feedback-modal';
    modal.className = 'fixed inset-0 z-[10000] hidden items-center justify-center';
    modal.innerHTML = `
      <div class="feedback-modal-backdrop absolute inset-0 bg-black bg-opacity-50"></div>
      <div class="feedback-modal-content relative bg-white rounded-xl shadow-2xl w-[90%] max-w-lg max-h-[90vh] overflow-hidden">
        <div class="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 class="text-xl font-semibold text-gray-900 m-0">Send Feedback</h2>
          <button type="button" class="bg-transparent border-0 cursor-pointer p-1 rounded-md text-gray-400 hover:bg-gray-100 hover:text-gray-600 transition-all duration-200 feedback-modal-close" aria-label="Close feedback form">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>
        </div>

        <div class="p-6 max-h-[calc(90vh-80px)] overflow-y-auto">
          <form id="feedback-form">
            <div class="feedback-form-group mb-5">
              <label for="feedback-message" class="block mb-1.5 font-medium text-gray-700 text-sm">Your feedback *</label>
              <textarea
                id="feedback-message"
                name="message"
                required
                placeholder="Tell us what you think about this page, what's working well, or what could be improved..."
                rows="4"
                class="w-full p-3 border-2 border-gray-200 rounded-lg text-sm transition-colors duration-200 focus:outline-none focus:border-blue-500 resize-vertical min-h-[100px]"
              ></textarea>
            </div>

            <div class="feedback-form-group mb-5">
              <label for="feedback-email" class="block mb-1.5 font-medium text-gray-700 text-sm">Email (optional)</label>
              <input
                type="email"
                id="feedback-email"
                name="email"
                placeholder="<EMAIL>"
                class="w-full p-3 border-2 border-gray-200 rounded-lg text-sm transition-colors duration-200 focus:outline-none focus:border-blue-500"
              />
              <small class="block mt-1 text-gray-500 text-xs">Leave your email if you'd like us to follow up with you</small>
            </div>

            <div class="feedback-form-group mb-5">
              <div class="mt-2">
                <button type="button" id="add-screenshot-btn" class="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-4 w-full cursor-pointer transition-all duration-200 flex items-center justify-center gap-2 text-sm text-gray-600 hover:bg-gray-100 hover:border-gray-400">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <circle cx="12" cy="13" r="4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  Add Screenshot
                </button>
                <div id="screenshot-preview" class="relative mt-3 rounded-lg overflow-hidden border border-gray-300 hidden">
                  <img id="screenshot-image" alt="Screenshot preview" class="w-full h-auto max-h-[200px] object-contain block" />
                  <button type="button" id="remove-screenshot-btn" class="absolute top-2 right-2 bg-black bg-opacity-70 text-white border-0 rounded-full w-7 h-7 cursor-pointer flex items-center justify-center transition-colors duration-200 hover:bg-opacity-90">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </button>
                </div>
              </div>
            </div>

            <div class="flex gap-3 justify-end mt-6 pt-5 border-t border-gray-200">
              <button type="button" id="feedback-cancel-btn" class="px-5 py-2.5 rounded-md text-sm font-medium cursor-pointer transition-all duration-200 border-0 flex items-center gap-1.5 bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200">
                Cancel
              </button>
              <button type="submit" id="feedback-submit-btn" class="px-5 py-2.5 rounded-md text-sm font-medium cursor-pointer transition-all duration-200 border-0 flex items-center gap-1.5 bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-60 disabled:cursor-not-allowed">
                <span class="feedback-submit-text">Send Feedback</span>
                <span class="feedback-submit-loading hidden items-center gap-1.5">
                  <svg class="feedback-spinner" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2v4M12 18v4M4.93 4.93l2.83 2.83M16.24 16.24l2.83 2.83M2 12h4M18 12h4M4.93 19.07l2.83-2.83M16.24 7.76l2.83-2.83" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  Sending...
                </span>
              </button>
            </div>
          </form>

          <div id="feedback-success" class="text-center p-5 hidden">
            <div class="mb-4">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="text-green-500 mx-auto">
                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M22 4L12 14.01l-3-3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-3 mt-0">Thank you for your feedback!</h3>
            <p class="text-gray-600 leading-relaxed mb-5 mt-0">We appreciate you taking the time to help us improve. Your feedback has been sent to our team.</p>
            <button type="button" id="feedback-success-close" class="px-5 py-2.5 rounded-md text-sm font-medium cursor-pointer transition-all duration-200 border-0 flex items-center gap-1.5 bg-blue-600 text-white hover:bg-blue-700 mx-auto">
              Close
            </button>
          </div>
        </div>
      </div>
    `;
    
    document.body.appendChild(modal);
    this.modal = modal;
  }

  bindEvents() {
    const floatingBtn = document.getElementById('feedback-floating-btn');
    const modal = document.getElementById('feedback-modal');
    const closeBtn = modal.querySelector('.feedback-modal-close');
    const cancelBtn = document.getElementById('feedback-cancel-btn');
    const backdrop = modal.querySelector('.feedback-modal-backdrop');
    const form = document.getElementById('feedback-form');
    const addScreenshotBtn = document.getElementById('add-screenshot-btn');
    const removeScreenshotBtn = document.getElementById('remove-screenshot-btn');
    const successCloseBtn = document.getElementById('feedback-success-close');

    // Open modal
    floatingBtn.addEventListener('click', () => this.openModal());

    // Close modal
    closeBtn.addEventListener('click', () => this.closeModal());
    cancelBtn.addEventListener('click', () => this.closeModal());
    backdrop.addEventListener('click', () => this.closeModal());
    successCloseBtn.addEventListener('click', () => this.closeModal());

    // Form submission
    form.addEventListener('submit', (e) => this.handleSubmit(e));

    // Screenshot functionality
    addScreenshotBtn.addEventListener('click', () => this.addScreenshot());
    removeScreenshotBtn.addEventListener('click', () => this.removeScreenshot());

    // Escape key to close modal
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isModalOpen()) {
        this.closeModal();
      }
    });
  }

  openModal() {
    this.modal.classList.remove('hidden');
    this.modal.classList.add('flex');
    document.body.style.overflow = 'hidden';

    // Focus the message textarea
    setTimeout(() => {
      document.getElementById('feedback-message').focus();
    }, 100);
  }

  closeModal() {
    this.modal.classList.add('hidden');
    this.modal.classList.remove('flex');
    document.body.style.overflow = '';
    this.resetForm();
  }

  isModalOpen() {
    return this.modal && this.modal.classList.contains('flex');
  }

  resetForm() {
    const form = document.getElementById('feedback-form');
    const successDiv = document.getElementById('feedback-success');

    form.reset();
    form.classList.remove('hidden');
    successDiv.classList.add('hidden');
    this.removeScreenshot();
    this.isSubmitting = false;
    this.updateSubmitButton();
  }

  async addScreenshot() {
    try {
      const screenshotData = await this.screenshotCapture.startSelection();
      if (screenshotData) {
        this.screenshotBlob = screenshotData;
        this.showScreenshotPreview(screenshotData);
      }
    } catch (error) {
      console.error('Failed to capture screenshot:', error);
      this.showError('Failed to capture screenshot. Please try again.');
    }
  }

  removeScreenshot() {
    this.screenshotBlob = null;
    const preview = document.getElementById('screenshot-preview');
    preview.classList.add('hidden');
  }

  showScreenshotPreview(screenshotData) {
    const preview = document.getElementById('screenshot-preview');
    const img = document.getElementById('screenshot-image');

    img.src = screenshotData;
    preview.classList.remove('hidden');
  }

  async handleSubmit(e) {
    e.preventDefault();
    
    if (this.isSubmitting) return;
    
    this.isSubmitting = true;
    this.updateSubmitButton();

    const formData = new FormData(e.target);
    const data = {
      message: formData.get('message'),
      email: formData.get('email'),
      page_url: window.location.href
    };

    // Add screenshot if present
    if (this.screenshotBlob) {
      data.screenshot = this.screenshotBlob.split(',')[1]; // Remove data URL prefix
    }

    try {
      const response = await fetch('/feedbacks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify(data)
      });

      const result = await response.json();

      if (response.ok && result.status === 'success') {
        this.showSuccess();
      } else {
        throw new Error(result.errors ? result.errors.join(', ') : 'Failed to submit feedback');
      }
    } catch (error) {
      console.error('Failed to submit feedback:', error);
      this.showError(error.message || 'Failed to submit feedback. Please try again.');
    } finally {
      this.isSubmitting = false;
      this.updateSubmitButton();
    }
  }

  updateSubmitButton() {
    const submitBtn = document.getElementById('feedback-submit-btn');
    const submitText = submitBtn.querySelector('.feedback-submit-text');
    const submitLoading = submitBtn.querySelector('.feedback-submit-loading');

    if (this.isSubmitting) {
      submitBtn.disabled = true;
      submitText.classList.add('hidden');
      submitLoading.classList.remove('hidden');
      submitLoading.classList.add('flex');
    } else {
      submitBtn.disabled = false;
      submitText.classList.remove('hidden');
      submitLoading.classList.add('hidden');
      submitLoading.classList.remove('flex');
    }
  }

  showSuccess() {
    const form = document.getElementById('feedback-form');
    const successDiv = document.getElementById('feedback-success');

    form.classList.add('hidden');
    successDiv.classList.remove('hidden');
  }

  showError(message) {
    // Simple error display - could be enhanced with a proper notification system
    alert(`Error: ${message}`);
  }
}

// Auto-initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.feedbackWidget = new FeedbackWidget();
});
