class FeedbacksController < WebApplicationController
  include <PERSON><PERSON>

  
  def create
    @feedback = Feedback.new(feedback_params)
    @feedback.page_url = request.referer || params[:page_url]
    @feedback.user_agent = request.user_agent
    @feedback.user = current_user if current_user.present?

    # Handle screenshot upload if present
    if params[:screenshot].present?
      begin
        # Decode base64 screenshot data
        screenshot_data = params[:screenshot]
        fileboy_image_id = upload_image_from_base64(screenshot_data)
        @feedback.fileboy_image_id = fileboy_image_id if fileboy_image_id.present?
      rescue => e
        Rails.logger.error "Failed to process screenshot: #{e.message}"
        raise "Failed to process screenshot: #{e.message}"
        # Continue without screenshot rather than failing the entire feedback
      end
    end

    if @feedback.save
      # Send notification email to administrators
      begin
        FeedbackMailer.new_feedback(@feedback).deliver_later
      rescue => e
        Rails.logger.error "Failed to send feedback notification email: #{e.message}"
      end
      
      render json: { 
        status: 'success', 
        message: 'Thank you for your feedback! We appreciate you taking the time to help us improve.' 
      }
    else
      render json: { 
        status: 'error', 
        errors: @feedback.errors.full_messages 
      }, status: :unprocessable_entity
    end
  end

  private

  def feedback_params
    params.permit(:message, :email)
  end

  def upload_screenshot_to_fileboy(image_file)
    upload_image(image_file)
  rescue => e
    Rails.logger.error "Fileboy upload error: #{e.message}"
    nil
  end
end
