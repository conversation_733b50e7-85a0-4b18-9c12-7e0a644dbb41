module Fileboy
  extend ActiveSupport::Concern

  require 'net/http'
  require 'uri'
  require 'tempfile'

  # PNG placeholder of a camera slightly faded
  PLACEHOLDER_IMAGE_ID = '5ae3fca6-77d6-4a47-b721-7cd8981dcc5b'

  def upload_image image
    image_file = image.tempfile
    image_name = image.original_filename
    image_content_type = MIME::Types.type_for(image_name).first.content_type

    uri = URI.parse('https://api.fileboy.io/files/upload')
    request = Net::HTTP::Post.new(uri)

    request['Accept'] = 'application/json'
    request['Authorization'] = 'public 92ff4093-f09c-4ab0-9851-cf76204f019c'

    # The JSON data should be sent as a part of the multipart form data
    data_json = { name: image_name, mime: image_content_type }.to_json

    form_data = {
      'file' => UploadIO.new(image_file, image_content_type, image_name),
      'data' => data_json
    }

    request.set_form(form_data, 'multipart/form-data')

    response = Net::HTTP.start(uri.hostname, uri.port, use_ssl: uri.scheme == 'https') do |http|
      http.request(request)
    end

    if response.code == '200'
      result = JSON.parse(response.body)
      result['data']['id']
    else
      # Handle error
      puts response.code
      puts response.body
    end
  end

  def upload_image_from_base64(base64_image, name = 'image.png')
    if base64_image.include?(',')
      # Remove data URL prefix if present (data:image/png;base64,)
      base64_image = base64_image.split(',').last
    end
    
    decoded_image = Base64.decode64(base64_image)
    
    # Create a temporary file for the screenshot
    temp_file = Tempfile.new(['image', '.png'])
    temp_file.binmode
    temp_file.write(decoded_image)
    temp_file.rewind

    image_file = temp_file
    image_name = name
    image_content_type = 'image/png'

    uri = URI.parse('https://api.fileboy.io/files/upload')
    request = Net::HTTP::Post.new(uri)

    request['Accept'] = 'application/json'
    request['Authorization'] = 'public 92ff4093-f09c-4ab0-9851-cf76204f019c'

    # The JSON data should be sent as a part of the multipart form data
    data_json = { name: image_name, mime: image_content_type }.to_json

    form_data = {
      'file' => UploadIO.new(image_file, image_content_type, image_name),
      'data' => data_json
    }

    request.set_form(form_data, 'multipart/form-data')

    response = Net::HTTP.start(uri.hostname, uri.port, use_ssl: uri.scheme == 'https') do |http|
      http.request(request)
    end

    if response.code == '200'
      temp_file.close
      temp_file.unlink

      result = JSON.parse(response.body)
      result['data']['id']
    else
      # Handle error
      puts response.code
      puts response.body
    end
  end

  def upload_image_from_url(image_url)
    uri = URI.parse('https://api.fileboy.io/files/upload')
    request = Net::HTTP::Post.new(uri)

    request['Accept'] = 'application/json'
    request['Authorization'] = 'public 92ff4093-f09c-4ab0-9851-cf76204f019c'
    request['Content-Type'] = 'application/json'

    body = { url: image_url, expect: "image/*" }.to_json
    request.body = body

    response = Net::HTTP.start(uri.hostname, uri.port, use_ssl: uri.scheme == 'https') do |http|
      http.request(request)
    end

    if response.code == '200'
      result = JSON.parse(response.body)
      result['data']['id']
    else
      # Handle error
      puts response.code
      puts response.body
    end
  end

  def upload_audio audio_file
    audio_content_type = 'audio/mpeg'

    uri = URI.parse('https://api.fileboy.io/files/upload')
    request = Net::HTTP::Post.new(uri)

    request['Accept'] = 'application/json'
    request['Authorization'] = 'public 92ff4093-f09c-4ab0-9851-cf76204f019c'

    # The JSON data should be sent as a part of the multipart form data
    data_json = { name: 'output.mp3', mime: audio_content_type }.to_json

    form_data = {
      'file' => UploadIO.new(audio_file, audio_content_type),
      'data' => data_json
    }

    request.set_form(form_data, 'multipart/form-data')

    response = Net::HTTP.start(uri.hostname, uri.port, use_ssl: uri.scheme == 'https') do |http|
      http.request(request)
    end

    if response.code == '200'
      result = JSON.parse(response.body)
      result['data']['id']
    else
      # Handle error
      puts response.code
      puts response.body
    end
  end

  def self.upload_sheet(xlsx_file, name, sheet_content_type = 'application/vnd.ms-excel')
    uri = URI.parse('https://api.fileboy.io/files/upload')
    request = Net::HTTP::Post.new(uri)

    request['Accept'] = 'application/json'
    request['Authorization'] = 'public 92ff4093-f09c-4ab0-9851-cf76204f019c'

    # The JSON data should be sent as a part of the multipart form data
    data_json = { name: "#{name}.xlsx", mime: sheet_content_type }.to_json

    form_data = {
      'file' => UploadIO.new(xlsx_file, sheet_content_type),
      'data' => data_json
    }

    request.set_form(form_data, 'multipart/form-data')

    response = Net::HTTP.start(uri.hostname, uri.port, use_ssl: uri.scheme == 'https') do |http|
      http.request(request)
    end

    if response.code == '200'
      result = JSON.parse(response.body)
      result['data']['id']
    else
      # Handle error
      puts response.code
      puts response.body
    end
  end

  def self.upload_pdf(pdf_content, name)
    pdf_content_type = 'application/pdf'

    uri = URI.parse('https://api.fileboy.io/files/upload')
    request = Net::HTTP::Post.new(uri)

    request['Accept'] = 'application/json'
    request['Authorization'] = 'public 92ff4093-f09c-4ab0-9851-cf76204f019c'

    # The JSON data should be sent as a part of the multipart form data
    data_json = { name: "#{name}.pdf", mime: pdf_content_type }.to_json

    temp_pdf_file = Tempfile.new(['temp_file', '.pdf'])
    temp_pdf_file.binmode
    temp_pdf_file.write(pdf_content)
    temp_pdf_file.rewind

    form_data = {
      'file' => UploadIO.new(temp_pdf_file, pdf_content_type),
      'data' => data_json
    }

    request.set_form(form_data, 'multipart/form-data')

    response = Net::HTTP.start(uri.hostname, uri.port, use_ssl: uri.scheme == 'https') do |http|
      http.request(request)
    end

    temp_pdf_file.close
    temp_pdf_file.unlink

    if response.code == '200'
      result = JSON.parse(response.body)
      result['data']['id']
    else
      # Handle error
      puts response.code
      puts response.body
    end
  end

  def upload_document(file)
    name = file.original_filename
    file_content_type = file.content_type

    uri = URI.parse('https://api.fileboy.io/files/upload')
    request = Net::HTTP::Post.new(uri)

    request['Accept'] = 'application/json'
    request['Authorization'] = 'public 92ff4093-f09c-4ab0-9851-cf76204f019c'

    # The JSON data should be sent as a part of the multipart form data
    data_json = { name: name, mime: file_content_type }.to_json

    form_data = {
      'file' => UploadIO.new(file, file_content_type),
      'data' => data_json
    }

    request.set_form(form_data, 'multipart/form-data')

    response = Net::HTTP.start(uri.hostname, uri.port, use_ssl: uri.scheme == 'https') do |http|
      http.request(request)
    end

    if response.code == '200'
      result = JSON.parse(response.body)
      result['data']['id']
    else
      # Handle error
      puts response.code
      puts response.body
    end
  end

end
