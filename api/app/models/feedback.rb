# == Schema Information
#
# Table name: feedbacks
#
#  id               :bigint           not null, primary key
#  email            :string
#  message          :text             not null
#  page_url         :string           not null
#  user_agent       :string
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  fileboy_image_id :string
#  user_id          :bigint
#
# Indexes
#
#  index_feedbacks_on_created_at        (created_at)
#  index_feedbacks_on_fileboy_image_id  (fileboy_image_id)
#  index_feedbacks_on_page_url          (page_url)
#  index_feedbacks_on_user_id           (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (user_id => users.id)
#
class Feedback < ApplicationRecord
  belongs_to :user, optional: true

  validates :message, presence: true
  validates :page_url, presence: true

  scope :recent, -> { order(created_at: :desc) }
  scope :with_screenshots, -> { where.not(fileboy_image_id: nil) }
  scope :without_screenshots, -> { where(fileboy_image_id: nil) }

  def has_screenshot?
    fileboy_image_id.present?
  end

  def screenshot_url
    return nil unless has_screenshot?
    "https://www.developingexperts.com/file-cdn/files/get/#{fileboy_image_id}"
  end

  def user_name
    user&.name || email || 'Anonymous'
  end

  def formatted_created_at
    created_at.strftime('%B %d, %Y at %I:%M %p')
  end
end
