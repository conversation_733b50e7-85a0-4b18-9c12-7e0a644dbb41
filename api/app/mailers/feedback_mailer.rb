class FeedbackMailer < ApplicationMailer
  default from: '<EMAIL>'

  def new_feedback(feedback)
    @feedback = feedback
    @user = feedback.user
    @user_name = feedback.user_name
    @message = feedback.message
    @email = feedback.email
    @page_url = feedback.page_url
    @user_agent = feedback.user_agent
    @screenshot_url = feedback.screenshot_url
    @created_at = feedback.formatted_created_at
    @has_screenshot = feedback.has_screenshot?

    # Add user context for PostHog tracking
    headers['X-User-ID'] = @user.id if @user
    headers['X-Mailer-Action'] = 'new_feedback'

    mail(
      to: feedback_admin_emails,
      subject: "New Feedback Submitted - #{@page_url&.split('/')&.last || 'Unknown Page'}"
    )
  end

  private

  def feedback_admin_emails
    # Send feedback notifications to admin team
    [
      '<EMAIL>',
      '<EMAIL>',
      'katie<PERSON><PERSON>@developingexperts.com'
    ]
  end
end
