<!DOCTYPE html>
<html class="<%= "dark bg-dark-blue" if ai_controllers.include? controller_name %>" lang="<%= I18n.locale.presence || 'en' %>">
<head>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= page_title("School", "DE") %></title>
  <script src="https://cdn.jsdelivr.net/npm/@cd2/fileboy-browser@0.12.1-alpha.7"></script>
  <script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
  <link href="https://fonts.googleapis.com/css2?family=Atkinson+Hyperlegible:wght@400;700&family=Inter:wght@400;700&family=Roboto:wght@400;700&family=Lato:wght@400;700&family=Open+Sans:wght@400;700&display=swap" rel="stylesheet">
  <%= csrf_meta_tags %>
  <%= csp_meta_tag %>

  <%= javascript_importmap_tags %>
  <%= stylesheet_link_tag 'application' %>
  <%= javascript_include_tag 'application', type: "module" %>
  <%= javascript_include_tag "application", "data-turbo-track": "reload" %>
  <%= javascript_include_tag "nice-select", type: "module" %>
  <%= stylesheet_link_tag "tailwind", "inter-font" %>
  <%= javascript_include_tag "shepherd/init", type: "module" %>
  <%= stylesheet_link_tag "https://cdn.jsdelivr.net/npm/shepherd.js@14.5.0/dist/css/shepherd.css", media: "all" %>

  <!-- Feedback Widget -->
  <%= stylesheet_link_tag "feedback_widget" %>
  <%= javascript_include_tag "area_selector", type: "module" %>
  <%= javascript_include_tag "screenshot_capture", type: "module" %>
  <%= javascript_include_tag "feedback_widget", type: "module" %>

  <!-- Start of HubSpot Embed Code -->
  <script>
      window.hsConversationsSettings = { loadImmediately: false }
  </script>
  <script
    type="text/javascript"
    id="hs-script-loader"
    async
    defer
    src="//js-na1.hs-scripts.com/14541915.js"
  ></script>
  <!-- End of HubSpot Embed Code -->
  <%= javascript_include_tag "hubspot-intercom", type: "module" %>
  <link
    rel="stylesheet"
    href="https://cdn.jsdelivr.net/npm/choices.js/public/assets/styles/choices.min.css"
    />
  <script src="https://cdn.jsdelivr.net/npm/choices.js/public/assets/scripts/choices.min.js"></script>
  <script defer src="https://kit.fontawesome.com/7fae4ae66d.js" crossorigin="anonymous"></script>
  <%= render './layouts/common_header' %>
  <script defer src="https://cdn.jsdelivr.net/npm/htmx.org@1.9.6"></script>
  <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js"></script>
</head>
<style>
    .flash {
        padding: 10px;
        border: 1px solid #ddd;
        margin: 10px;
    }

    .notice {
        background-color: #e7f4e4;
        color: #2d572c;
    }

    .alert {
        background-color: #ffc2c2;
        color: #ff4545;
    }
</style>
<body class="school-layout">
<div class="bg-dark-blue">
  <div class="sidebar-accordion">
    <%= render 'htmx_components/school_sidebar', controller_name: controller_name %>
  </div>
  <!-- Content area -->
  <div class="bg-dark-blue text-white flex-1 content-container min-h-screen">
    <div class="flex justify-between items-center px-10 h-20">
      <%= render '/htmx_components/static_session_header', user: @current_user %>
    </div>
    <%= render 'shared/flash_banner' %>
    <%= render 'shared/flag_media_dialog' %>
    <div id="preview-video-loader" style="display: none;"></div>
    <%= yield %>
    <div class="border-t border-white/20 mt-12 py-12 text-center text-sm">
      Copyright © <%= Time.current.year %> Developing Experts Ltd.
    </div>
  </div>
</div>
<% if @current_user&.beta_feature_enabled?(:september_1) %>
  <script src="https://cdn.jsdelivr.net/gh/isaul32/ckeditor5@c3463fe834049bf5d805d1d22402108a9c0576bd/packages/ckeditor5-build-classic/build/ckeditor.js"></script>
<% else %>
  <script src="https://cdn.ckeditor.com/ckeditor5/40.2.0/classic/ckeditor.js"></script>
<% end %>
<script>
    class FileboyUploadAdapter {
        constructor(loader) {
            this.loader = loader;
        }

        upload() {
            return this.loader.file.then(file => {
                return new Promise((resolve, reject) => {
                    const data = new FormData();
                    data.append('upload', file);

                    fetch('/images', {
                        method: 'POST',
                        body: data
                    })
                        .then(response => response.json())
                        .then(result => {
                            if (result.uploaded) {
                                resolve({default: result.url});
                            } else {
                                reject(result.error.message);
                            }
                        })
                        .catch(error => {
                            reject('File upload failed');
                        });
                });
            });
        }
    }

    function FileboyUploadAdapterPlugin(editor) {
        editor.plugins.get('FileRepository').createUploadAdapter = (loader) => {
            return new FileboyUploadAdapter(loader);
        };
    }

    document.querySelectorAll('.ckeditor').forEach(function (e) {
        ClassicEditor
            .create(e, {
                math: {
                  engine: 'mathjax',
                  outputType: 'script',
                  forceOutputType: false,
                  enablePreview: true
                },
                extraPlugins: [FileboyUploadAdapterPlugin],
                // ... other CKEditor configurations ...
            })
            .catch(error => {
                console.error(error);
            });
    });

</script>
<div id="toast-container"></div>
<div hx-trigger="load" hx-get="/dev_toolbar" hx-swap="outerHtml" hx-target="this"></div>
</body>
