<% content_for :title do %>
  New Feedback Submitted
<% end %>

<h1>New Feedback Submitted</h1>

<div style="background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
  <p><strong>A user has submitted feedback through the feedback widget.</strong></p>
</div>

<h2>Feedback Details</h2>
<table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
  <tr>
    <th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd; width: 150px;">Submitted At</th>
    <td style="padding: 8px; border-bottom: 1px solid #ddd;"><%= @created_at %></td>
  </tr>
  <tr>
    <th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;">Page URL</th>
    <td style="padding: 8px; border-bottom: 1px solid #ddd;">
      <% if @page_url.present? %>
        <a href="<%= @page_url %>" style="color: #556cd6;"><%= @page_url %></a>
      <% else %>
        Not provided
      <% end %>
    </td>
  </tr>
  <tr>
    <th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;">User</th>
    <td style="padding: 8px; border-bottom: 1px solid #ddd;">
      <% if @user.present? %>
        <%= @user.name %> (<%= @user.email %>)
        <% if @user.school.present? %>
          <br><small>School: <%= @user.school.name %></small>
        <% end %>
      <% elsif @email.present? %>
        <%= @email %> (Guest)
      <% else %>
        Anonymous
      <% end %>
    </td>
  </tr>
  <% if @user_agent.present? %>
  <tr>
    <th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;">Browser</th>
    <td style="padding: 8px; border-bottom: 1px solid #ddd; font-family: monospace; font-size: 12px;"><%= @user_agent %></td>
  </tr>
  <% end %>
  <tr>
    <th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;">Screenshot</th>
    <td style="padding: 8px; border-bottom: 1px solid #ddd;">
      <% if @has_screenshot %>
        <span style="color: #28a745;">✓ Included</span>
      <% else %>
        <span style="color: #6c757d;">Not included</span>
      <% end %>
    </td>
  </tr>
</table>

<h2>Feedback Message</h2>
<div style="background-color: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
  <p style="margin: 0; white-space: pre-wrap;"><%= @message %></p>
</div>

<% if @has_screenshot && @screenshot_url.present? %>
<h2>Screenshot</h2>
<div style="background-color: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px; margin-bottom: 20px; text-align: center;">
  <p style="margin-bottom: 10px;">
    <a href="<%= @screenshot_url %>" style="color: #556cd6;">View Full Screenshot</a>
  </p>
  <img src="<%= @screenshot_url %>" alt="User Screenshot" style="max-width: 100%; height: auto; border: 1px solid #ddd; border-radius: 5px;" />
</div>
<% end %>

<hr style="margin: 30px 0; border: none; border-top: 1px solid #dee2e6;">

<p style="color: #6c757d; font-size: 14px;">
  This is an automated notification from the feedback widget. 
  <% if @user.present? %>
    You can respond to this feedback by contacting the user directly at <%= @user.email %>.
  <% elsif @email.present? %>
    You can respond to this feedback by contacting the user directly at <%= @email %>.
  <% end %>
</p>
